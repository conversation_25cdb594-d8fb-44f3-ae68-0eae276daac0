// models/userProgressModel.js
import { models, model, Schema } from 'mongoose';

const userProgressSchema = new Schema({
  userEmail: {
    type: String,
    required: true,
    ref: 'UsuarioAcademy'
  },
  // Progreso de videos
  videosWatched: [{
    videoId: String,
    videoName: String,
    category: String,
    completedAt: {
      type: Date,
      default: Date.now
    },
    watchTime: {
      type: Number, // en segundos
      default: 0
    },
    progress: {
      type: Number, // porcentaje 0-100
      default: 0
    }
  }],
  
  // Tiempo total de estudio
  totalStudyTime: {
    type: Number, // en minutos
    default: 0
  },
  
  // Sesiones de estudio
  studySessions: [{
    startTime: Date,
    endTime: Date,
    duration: Number, // en minutos
    activity: String // 'video', 'reading', 'practice'
  }],
  
  // Progreso por categoría
  categoryProgress: {
    type: {
      basico: {
        completed: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
      },
      intermedio: {
        completed: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
      },
      avanzado: {
        completed: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
      }
    },
    default: {
      basico: { completed: 0, total: 0 },
      intermedio: { completed: 0, total: 0 },
      avanzado: { completed: 0, total: 0 }
    }
  },
  
  // Notas del usuario
  videoNotes: [{
    videoId: String,
    note: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Videos favoritos
  favoriteVideos: [String],
  
  // Certificaciones
  certificates: [{
    type: String, // 'basico', 'intermedio', 'avanzado', 'completo'
    issuedAt: {
      type: Date,
      default: Date.now
    },
    certificateId: String,
    validatedBy: {
      type: String,
      default: 'Germayori - La Diosa del Trading'
    }
  }],
  
  // Estadísticas generales
  stats: {
    type: {
      loginStreak: { type: Number, default: 0 },
      lastLoginDate: Date,
      totalLogins: { type: Number, default: 0 },
      averageSessionTime: { type: Number, default: 0 }
    },
    default: {
      loginStreak: 0,
      totalLogins: 0,
      averageSessionTime: 0
    }
  },
  
  // Configuración del perfil
  profile: {
    type: {
      profileImage: String,
      bio: String,
      goals: [String],
      preferences: {
        playbackSpeed: { type: Number, default: 1 },
        autoplay: { type: Boolean, default: true },
        notifications: { type: Boolean, default: true }
      }
    },
    default: {
      preferences: {
        playbackSpeed: 1,
        autoplay: true,
        notifications: true
      }
    }
  },
  
  // Fechas importantes
  startDate: {
    type: Date,
    default: Date.now
  },
  lastActivity: {
    type: Date,
    default: Date.now
  },
  
  // Progreso general
  overallProgress: {
    type: Number, // porcentaje 0-100
    default: 0
  }
}, {
  timestamps: true
});

// Índices para optimizar consultas
userProgressSchema.index({ userEmail: 1 });
userProgressSchema.index({ 'videosWatched.videoId': 1 });
userProgressSchema.index({ lastActivity: -1 });

// Métodos del esquema simplificados
userProgressSchema.methods.calculateOverallProgress = function() {
  const totalVideos = (this.categoryProgress?.basico?.total || 0) +
                     (this.categoryProgress?.intermedio?.total || 0) +
                     (this.categoryProgress?.avanzado?.total || 0);

  const completedVideos = (this.categoryProgress?.basico?.completed || 0) +
                         (this.categoryProgress?.intermedio?.completed || 0) +
                         (this.categoryProgress?.avanzado?.completed || 0);

  this.overallProgress = totalVideos > 0 ? Math.round((completedVideos / totalVideos) * 100) : 0;
  return this.overallProgress;
};

const UserProgress = models?.UserProgress || model('UserProgress', userProgressSchema);
export default UserProgress;
