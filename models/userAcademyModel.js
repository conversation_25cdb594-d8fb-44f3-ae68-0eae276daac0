// usuarioAcademyModel.js

import {models,model,Schema, now} from 'mongoose';
const usuarioAcademySchema = new Schema({
  name: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
  },
  isActive: {
    type: Boolean,
    default: true, // El usuario está activo por defecto
  },
  cutoffDate: {
    type: Date,
    default: Date.now, // Sin fecha de corte por defecto
  },
  canEnter: {
    type: Boolean,
    default: false, // Puede entrar por defecto
  },
  paymentId: {
    type: String,
    required: false
  },
  role: {
    type: String,
    default : 'BASICO',
    required: false
  },
  vendor: {
    type: Boolean,
    default : false
  },
  created: {
    type: String,
    required: true
  }
});

export const UsuarioAcademy = models?.UsuarioAcademy || model('UsuarioAcademy', usuarioAcademySchema);
export default UsuarioAcademy;
