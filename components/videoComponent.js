import { useEffect, useState } from 'react';

const VideosComponent = () => {
  const [videos, setVideos] = useState([]);

  useEffect(() => {
    const fetchVideos = async () => {
      const res = await fetch('/api/videos?folder=clases intermedio/');
      const data = await res.json();
      setVideos(data);
    };

    fetchVideos();
  }, []);

  return (
    <div>
      {videos.map((videoUrl, index) => (
        <video key={index} controls>
          <source src={videoUrl} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      ))}
    </div>
  );
};

export default VideosComponent;
