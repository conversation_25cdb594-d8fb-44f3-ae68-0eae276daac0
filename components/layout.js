import React, { useState, useEffect } from "react";
import classNames from "classnames";
import SidebarComponent from "./sidebar";
import { getSession } from "next-auth/react";
   
const Layout = (props) => {
  const [collapsed, setSideBarCollapsed] = useState(false);
  const [videos, setVideos] = useState([]);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [nameVideo, setNameVideo] = useState("");
  const [isLoadingVideos, setIsLoadingVideos] = useState(true); // Estado para controlar la carga de videos

  useEffect(() => {
    const fetchVideos = async () => {
      const session = await getSession();
      try {
        const res = await fetch("/api/videos?user=" + session.user.email);
        const data = await res.json();
        setVideos(data);
      } catch (error) {
        console.error("Error fetching videos:", error);
      } finally {
        setIsLoadingVideos(false); // Cuando termina la carga de videos, establece isLoadingVideos a false
      }
    };

    fetchVideos();
  }, []);

  const handleVideoSelect = (videoUrl) => {
    setSelectedVideo(videoUrl);
  };

  const handleContextMenu = (e) => {
    e.preventDefault();
  };

  return (
    <div
      className={classNames({
        "lg:grid lg:min-h-screen": true,
        "lg:grid-cols-sidebar": !collapsed, // Sidebar visible en pantallas grandes
        "lg:grid-cols-sidebar-collapsed": collapsed, // Sidebar colapsado en pantallas grandes
        "transition-[grid-template-columns] duration-500 ease-in-out": true,
        // En pantallas pequeñas (móviles), se organiza todo en un bloque vertical
        "flex flex-col": true,
      })}
    >
      <SidebarComponent
        setNameVideo={setNameVideo}
        videos={videos}
        collapsed={collapsed}
        setCollapsed={() => setSideBarCollapsed((prev) => !prev)}
        onVideoSelect={handleVideoSelect}
        className={"transition-all"}
      />
      <div className="flex flex-col justify-center items-center">
        {isLoadingVideos ? (
          <div className="flex items-center justify-center h-screen">
            <div className="animate-spin rounded-full h-20 w-20 border-t-2 border-b-2 border-secondary-500"></div>
          </div>
        ) : (
          <>
            <h1 className="text-4xl text-secondary-500">
              {nameVideo && nameVideo}
            </h1>
            {selectedVideo && (
              <div className="mt-32">
                {selectedVideo.includes(".mp4") ? (
                  <video
                    key={selectedVideo}
                    controls
                    autoPlay
                    controlsList="nodownload"
                    onContextMenu={handleContextMenu}
                  >
                    <source src={selectedVideo} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                ) : (
                  <img src={selectedVideo} alt="Selected Image" />
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Layout;
