import React from "react";
import Container from "./container";
import FloatingButton from "./floatingButton";
import Image from "next/image";
import whatsAppIcon from "../public/img/ws.png"

const Cta = ({title, subtitle}) => {
  return (
    <Container className="mb-20">
      <div className="flex flex-wrap items-center justify-between w-full max-w-4xl gap-5 mx-auto text-white bg-amber-600 px-7 py-7 lg:px-12 lg:py-12 lg:flex-nowrap rounded-xl">
        <div className="flex-grow text-center lg:text-left">
          <h2 className="text-2xl font-medium lg:text-3xl">
            {title}
          </h2>
          <p className="mt-2 font-medium text-white text-opacity-90 lg:text-xl">
            {subtitle}
          </p>
        </div>
        <div className="flex-shrink-0 w-full text-center lg:w-auto">
          <FloatingButton position="block" route="https://wa.me/50761559284">
            <Image
              className="grayscale hover:grayscale-0"
              src={whatsAppIcon}
              alt={""}
            />
          </FloatingButton>
        </div>
      </div>
    </Container>
  );
};

export default Cta;
