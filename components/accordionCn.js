import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";
import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
export function VideoAccordion({
  data,
  onVideoSelect,
  collapsed,
  setNameVideo,
}) {
  console.log(data, "accordion");
  const [userDatabase, setUserDatabase] = useState();
  const { data: session, status } = useSession();
  useEffect(() => {
    const fetchUser = async () => {
      if (session?.user?.email) {
        try {
          const response = await fetch(
            `/api/users?userEmail=${session.user.email}`
          );
          if (!response.ok)
            throw new Error("Respuesta de la API no fue exitosa.");
          const data = await response.json();
          setUserDatabase(data);
        } catch (error) {
          console.error("Error al recuperar los datos del usuario", error);
        }
      }
    };
    fetchUser();
  }, [session]);
  const handleClick = (e, video, name) => {
    e.preventDefault(); // Evitar la navegación
    let parseVideo = video.name.split(".");
    console.log(userDatabase);
    if (userDatabase.role === "completo") {
      onVideoSelect(video.url); // Llamar a la función de selección de video
      setNameVideo(name);
    } else if (
      userDatabase.role !== "completo" &&
      parseVideo[parseVideo.length - 1].toLowerCase() !== "mp4"
    ) {
      onVideoSelect(video.url); // Llamar a la función de selección de video
      setNameVideo(name);
    } else if (
      userDatabase.role !== "completo" &&
      parseVideo[parseVideo.length - 1].toLowerCase() === "mp4"
    ) {
      alert("no tienes una membresia activa");
    }
  };
  /* const filteredData = Object.keys(data)
    .filter(
      (key) => key !== "Trading Avanzado/" && key !== "Trading Intermedio/"
    )
    .reduce((obj, key) => {
      obj[key] = data[key];
      return obj;
    }, {}); */
  return (
    <Accordion type="single" collapsible className="w-full">
      {Object.keys(data).map((directory, index) => (
        <AccordionItem key={index} value={`item-${index + 1}`}>
          <AccordionTrigger>{directory.replace(/\//g, "")}</AccordionTrigger>
          <AccordionContent>
            <div className="video-scroll-area custom-scrollbar">
              <ul>
                {data[directory].map((video, vidIndex) => (
                  <li key={vidIndex}>
                    <button
                      className="truncate pb-5 text-secondary-200 ml-4 hover:text-secondary-100 transition-colors duration-200"
                      onClick={(e) =>
                        handleClick(
                          e,
                          video,
                          video.name.split("/").pop().split(" (")[0].split(".")[0]
                        )
                      }
                    >
                      {video.name.split("/").pop().split(" (")[0].split(".")[0]}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}
