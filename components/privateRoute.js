import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import Loading from "./loading";
const PrivateRoute = ({ children }) => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [userDatabase, setUserDatabase] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      if (session?.user?.email) {
        try {
          const response = await fetch(
            `/api/users?userEmail=${session.user.email}`
          );
          if (!response.ok)
            throw new Error("Respuesta de la API no fue exitosa.");
          const data = await response.json();
          setUserDatabase(data);
          setIsLoading(false);
        } catch (error) {
          console.error("Error al recuperar los datos del usuario", error);
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };

    fetchUser();
  }, [session]);

  if (status === "loading" || isLoading) {
    return <Loading />; // Puedes reemplazar esto con tu componente de carga
  }

  // Verificar sesión y membresía activa
  if (!session || !userDatabase || !userDatabase?.canEnter) {
    router.push("/login"); // Redirige a la página de inicio de sesión si no hay sesión o canEnter es false.
    return null;
  }

  return children;
};

export default PrivateRoute;
