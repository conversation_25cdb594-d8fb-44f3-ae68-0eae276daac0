// components/ProgressMigration.js
import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

const ProgressMigration = () => {
  const { data: session } = useSession();
  const [showMigration, setShowMigration] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationComplete, setMigrationComplete] = useState(false);

  useEffect(() => {
    // Verificar si hay datos en localStorage que necesiten migración
    const checkForLocalData = () => {
      // Verificar si ya se hizo la migración o se omitió
      const migrationSkipped = localStorage.getItem('migrationSkipped');
      const migrationCompleted = localStorage.getItem('migrationCompleted');

      if (migrationSkipped === 'true' || migrationCompleted === 'true') {
        return; // No mostrar el modal si ya se procesó
      }

      const hasLocalData =
        localStorage.getItem('watchedVideos') ||
        localStorage.getItem('favoriteVideos') ||
        localStorage.getItem('videoNotes') ||
        localStorage.getItem('videoProgress');

      if (hasLocalData && session?.user?.email) {
        setShowMigration(true);
      }
    };

    checkForLocalData();
  }, [session]);

  const handleMigration = async () => {
    if (!session?.user?.email) return;

    setIsMigrating(true);
    
    try {
      // Obtener datos de localStorage
      const savedWatched = localStorage.getItem('watchedVideos');
      const savedFavorites = localStorage.getItem('favoriteVideos');
      const savedNotes = localStorage.getItem('videoNotes');
      const savedProgress = localStorage.getItem('videoProgress');

      let migratedCount = 0;

      // Migrar videos vistos
      if (savedWatched) {
        const watchedVideos = JSON.parse(savedWatched);
        for (const videoId of watchedVideos) {
          try {
            await fetch('/api/progress', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                action: 'mark_video_watched',
                data: {
                  videoId,
                  videoName: videoId,
                  category: 'migrated',
                  watchTime: 0,
                  progressPercent: 100
                }
              })
            });
            migratedCount++;
          } catch (error) {
            console.error(`Error migrando video ${videoId}:`, error);
          }
        }
      }

      // Migrar favoritos
      if (savedFavorites) {
        const favoriteVideos = JSON.parse(savedFavorites);
        for (const videoId of favoriteVideos) {
          try {
            await fetch('/api/progress', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                action: 'toggle_favorite',
                data: { videoId }
              })
            });
            migratedCount++;
          } catch (error) {
            console.error(`Error migrando favorito ${videoId}:`, error);
          }
        }
      }

      // Migrar notas
      if (savedNotes) {
        const notes = JSON.parse(savedNotes);
        for (const [videoId, note] of Object.entries(notes)) {
          if (note.trim()) {
            try {
              await fetch('/api/progress', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  action: 'save_note',
                  data: { videoId, note }
                })
              });
              migratedCount++;
            } catch (error) {
              console.error(`Error migrando nota ${videoId}:`, error);
            }
          }
        }
      }

      console.log(`Migración completada: ${migratedCount} elementos migrados`);
      setMigrationComplete(true);

      // Marcar migración como completada
      localStorage.setItem('migrationCompleted', 'true');

      // Limpiar localStorage después de migración exitosa
      setTimeout(() => {
        localStorage.removeItem('watchedVideos');
        localStorage.removeItem('favoriteVideos');
        localStorage.removeItem('videoNotes');
        localStorage.removeItem('videoProgress');
        setShowMigration(false);
      }, 3000);

    } catch (error) {
      console.error('Error durante la migración:', error);
      alert('Error durante la migración. Algunos datos podrían no haberse transferido correctamente.');
    } finally {
      setIsMigrating(false);
    }
  };

  const handleSkipMigration = () => {
    setShowMigration(false);
    // Marcar que el usuario decidió no migrar
    localStorage.setItem('migrationSkipped', 'true');
  };

  // Función para resetear el estado de migración (útil para testing)
  const resetMigrationState = () => {
    localStorage.removeItem('migrationSkipped');
    localStorage.removeItem('migrationCompleted');
    setShowMigration(false);
  };

  if (!showMigration) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-primary-800 to-primary-900 border border-secondary-500/30 rounded-xl p-6 max-w-md w-full shadow-2xl">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-secondary-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-secondary-400 mb-2">
            Migración de Progreso
          </h3>
          <p className="text-secondary-200 text-sm">
            Hemos detectado datos de progreso guardados localmente. ¿Te gustaría transferirlos a tu cuenta para mantener tu progreso sincronizado?
          </p>
        </div>

        {migrationComplete ? (
          <div className="text-center">
            <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="text-green-400 font-semibold mb-2">¡Migración Completada!</p>
            <p className="text-secondary-200 text-sm">
              Tu progreso ha sido transferido exitosamente a tu cuenta.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex space-x-3">
              <button
                onClick={handleMigration}
                disabled={isMigrating}
                className="flex-1 bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200"
              >
                {isMigrating ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                    <span>Migrando...</span>
                  </div>
                ) : (
                  'Migrar Datos'
                )}
              </button>
              
              <button
                onClick={handleSkipMigration}
                disabled={isMigrating}
                className="flex-1 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200"
              >
                Omitir
              </button>
            </div>
            
            <p className="text-xs text-secondary-300 text-center">
              Si omites la migración, tus datos locales se mantendrán pero no se sincronizarán entre dispositivos.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgressMigration;
