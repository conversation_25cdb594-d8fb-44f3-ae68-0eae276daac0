"use client";
import Link from "next/link";
import { AiFillTrademarkCircle } from "react-icons/ai";
import { useSession } from "next-auth/react";
import { useState } from "react";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  TransitionChild,
} from "@headlessui/react";
import {
  Bars3Icon,
  CalendarIcon,
  ChartPieIcon,
  HomeIcon,
  UsersIcon,
  XMarkIcon,
  CpuChipIcon,
  NewspaperIcon,
} from "@heroicons/react/24/outline";

const navigation = [
  { route: "/", name: "Inicio", current: false, icon: HomeIcon },
  {
    route: "videos",
    name: "Videos Academia",
    current: true,
    icon: ChartPieIcon,
  },
  {
    route: "germa<PERSON>ri",
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    current: false,
    icon: CpuChipIcon,
  },
  {
    route: "noticias",
    name: "Noticias",
    current: false,
    icon: NewspaperIcon,
  },
  { route: "login", name: "<PERSON><PERSON>l", current: false, icon: UsersIcon },
  {
    route: "membership",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    current: false,
    icon: CalendarIcon,
  },
];
const herramientas = [
  {
    id: 1,
    name: "TradingView",
    href: "https://es.tradingview.com/",
    initial: "T",
    current: false,
  },
  {
    id: 2,
    name: "Calculadora de Pips",
    href: "https://www.myfxbook.com/es/forex-calculators/position-size",
    initial: "C",
    current: false,
  },
  {
    id: 3,
    name: "Calendario Economico",
    href: "https://www.myfxbook.com/es/forex-economic-calendar",
    initial: "E",
    current: false,
  },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function SidebarTailwindComponent({ children }) {
  const { data: session, status } = useSession();

  console.log(session);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <>
      <div>
        <Dialog
          open={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          className="relative z-50 lg:hidden "
        >
          <div className="fixed inset-0 bg-gray-900/80" />
          <div className="fixed inset-0 flex">
            <Dialog.Panel className="relative w-full max-w-xs bg-primary-800 p-6 overflow-y-auto">
              <button
                type="button"
                onClick={() => setSidebarOpen(false)}
                className="absolute top-4 right-4"
              >
                <XMarkIcon className="h-6 w-6 text-secondary-400" />
                <span className="sr-only">Close sidebar</span>
              </button>
              <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-primary-800 px-6 pb-2 custom-scrollbar">
                <div className="flex h-16 shrink-0 items-center">
                  <Link href="/">
                    <span className="flex items-center text-2xl font-medium text-secondary-400 dark:text-secondary-400">
                      <AiFillTrademarkCircle className="text-secondary-400 w-10 h-10 animate-pulse" />
                      <span className="text-secondary-400">unning Pips</span>
                    </span>
                  </Link>
                </div>
                {children}
                <nav className="flex flex-1 flex-col">
                  <ul role="list" className="flex flex-1 flex-col gap-y-7">
                    <li>
                      <ul role="list" className="-mx-2 space-y-1">
                        {navigation.map((item) => (
                          <li key={item.name}>
                            <Link
                              key={item.name}
                              href={item.route}
                              className={classNames(
                                item.current
                                  ? "bg-secondary-600 text-primary-900"
                                  : "text-secondary-200 hover:bg-secondary-700 hover:text-primary-900",
                                "group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6"
                              )}
                            >
                              <item.icon
                                aria-hidden="true"
                                className={classNames(
                                  item.current
                                    ? "text-trueGray-700"
                                    : "text-trueGray-900 group-hover:text-white",
                                  "h-6 w-6 shrink-0"
                                )}
                              />
                              {item.name}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </li>
                    <li>
                      <div className="text-xs font-semibold leading-6 text-indigo-200">
                        Herramientas
                      </div>
                      <ul role="list" className="-mx-2 mt-2 space-y-1">
                        {herramientas.map((team) => (
                          <li key={team.name}>
                            <a
                              href={team.href}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={classNames(
                                team.current
                                  ? "bg-trueGray-900 text-white"
                                  : "text-indigo-200 hover:bg-trueGray-900 hover:text-white",
                                "group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6"
                              )}
                            >
                              <span className="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-trueGray-800 bg-trueGray-900 text-[0.625rem] font-medium text-white">
                                {team.initial}
                              </span>
                              <span className="truncate">{team.name}</span>
                            </a>
                          </li>
                        ))}
                      </ul>
                    </li>
                  </ul>
                </nav>
              </div>
            </Dialog.Panel>
          </div>
        </Dialog>
        {/* Static sidebar for desktop */}
        <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
          {/* Sidebar component, swap this element with another sidebar if you like */}
          <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-primary-800 px-6 custom-scrollbar">
            <div className="flex h-16 shrink-0 items-center">
              <Link href="/">
                <span className="flex items-center text-2xl font-medium text-secondary-400 dark:text-secondary-400">
                  <AiFillTrademarkCircle className="text-secondary-400 w-10 h-10 animate-pulse" />
                  <span className="text-secondary-400">unning Pips</span>
                </span>
              </Link>
            </div>
            {children}
            <nav className="flex flex-1 flex-col">
              <ul role="list" className="flex flex-1 flex-col gap-y-7">
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <Link
                        key={item.name}
                        href={item.route}
                        className={classNames(
                          item.current
                            ? "bg-secondary-600 text-primary-900"
                            : "text-secondary-200 hover:bg-secondary-700 hover:text-primary-900",
                          "group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6"
                        )}
                      >
                        <item.icon
                          aria-hidden="true"
                          className={classNames(
                            item.current
                              ? "text-primary-900"
                              : "text-secondary-300 group-hover:text-primary-900",
                            "h-6 w-6 shrink-0"
                          )}
                        />
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
                <li>
                  <div className="text-xs font-semibold leading-6 text-indigo-200">
                    Herramientas
                  </div>
                  <ul role="list" className="-mx-2 mt-2 space-y-1">
                    {herramientas.map((team) => (
                      <li key={team.name}>
                        <a
                          href={team.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={classNames(
                            team.current
                              ? "bg-trueGray-900 text-white"
                              : "text-indigo-200 hover:bg-trueGray-900 hover:text-white",
                            "group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6"
                          )}
                        >
                          <span className="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-trueGray-800 bg-trueGray-900 text-[0.625rem] font-medium text-white">
                            {team.initial}
                          </span>
                          <span className="truncate">{team.name}</span>
                        </a>
                      </li>
                    ))}
                  </ul>
                </li>
                <li className="-mx-6 mt-auto">
                  <a
                    href="#"
                    className="flex items-center gap-x-4 px-6 py-3 text-sm font-semibold leading-6 text-white hover:bg-trueGray-900"
                  >
                    <img
                      alt=""
                      src={session.user.image}
                      className="h-8 w-8 rounded-full bg-trueGray-900"
                    />
                    <span className="sr-only">Tu Perfil</span>
                    <span aria-hidden="true">{session.user.name}</span>
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>

        <div className="sticky top-0 z-40 flex items-center gap-x-6 bg-primary-800 px-4 py-4 shadow-sm sm:px-6 lg:hidden">
          <button
            type="button"
            onClick={() => setSidebarOpen(true)}
            className="-m-2.5 p-2.5 text-secondary-200 lg:hidden"
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon aria-hidden="true" className="h-6 w-6" />
          </button>
          <div className="flex-1 text-sm font-semibold leading-6 text-secondary-400">
            Ruta de Aprendizaje
          </div>
          <a href="#">
            <span className="sr-only">Tu perfil</span>
            <img
              alt=""
              src={session.user.image}
              className="h-8 w-8 rounded-full bg-trueGray-900"
            />
          </a>
        </div>

        <main className="py-10 lg:pl-72">
          <div className="px-4 sm:px-6 lg:px-8">{/* Your content */}</div>
        </main>
      </div>
    </>
  );
}
