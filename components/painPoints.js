
import React from 'react';
import Step from './stepContainer';

const PainPoints = () => {
  const handleClick = async (event) => {
    const element = document.getElementById("contacto");
    if (!element) return;
    element.scrollIntoView({ behavior: "smooth" });
  };

  const issues = [
    {
      title: "No logras mantener una consistencia en tus operaciones.",
      icon: "🚫",
    },
    {
      title:
        "Tus emociones afectan tus decisiones y te desvían de tu estrategia.",
      icon: "🚫",
    },
    {
      title:
        "No cuentas con un plan de trading sólido ni gestión de riesgos efectiva.",
      icon: "🚫",
    },
    {
      title:
        "Operas de manera reactiva y sin un análisis profundo de los mercados.",
      icon: "🚫",
    },
    {
      title:
        "No dispones de herramientas avanzadas que te den una ventaja competitiva.",
      icon: "🚫",
    },
  ];

  return (
    <div className="flex flex-col items-center justify-center w-full m-auto">
      <div className="flex justify-center items-center bg-deep-blue p-8 w-full m-auto">
        <div className="flex flex-col w-5/6 m-auto max-w-2xl text-center">
          {issues.map((card, index) => (
            <Step key={index} description={card.title} />
          ))}
        </div>
      </div>
      <button
        onClick={handleClick}
        className="px-8 py-4 text-lg flex gap-2 font-medium text-center text-white bg-orange-600 rounded-md"
      >
        Quiero mejorar mis resultados
      </button>
    </div>
  );
};

export default PainPoints;
