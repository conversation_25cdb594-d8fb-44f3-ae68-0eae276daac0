import { getSession } from "next-auth/react";
export const payWithYappy = async (payment) => {
  const session = await getSession();
  try {
    const response = await fetch(
      `/api/pagosbg?payment=${payment}&customer=${session.user.email}`,
      { method: "GET" }
    );
    const data = await response.json()
    if (data.success) {
      window.location.href = data.url;
    } else {
      console.error("Error:", data.message);
    }

  } catch (error) {
    console.error("Hubo un problema con tu operación de fetch:", error);
  }
};