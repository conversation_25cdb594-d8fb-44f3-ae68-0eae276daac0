# Variables de entorno para RunningPips Academy
# ================================================

# Configuración de OpenAI para Germayori AI
OPENAI_API_KEY=sk-proj-tu_api_key_de_openai_aqui

# Configuración de autenticación NextAuth
SECRET=tu_secret_aqui
NEXTAUTH_URL=http://localhost:3000

# Configuración de Google OAuth
GOOGLE_CLIENT_ID=tu_google_client_id
GOOGLE_CLIENT_SECRET=tu_google_client_secret

# Configuración de MongoDB
MONGODB_URI=mongodb+srv://usuario:<EMAIL>/database

# Configuración de AWS S3
AWS_ACCESS_KEY_ID=tu_aws_access_key
AWS_SECRET_ACCESS_KEY=tu_aws_secret_key
AWS_REGION=us-east-2

# Configuración de Yappy (Pagos)
MERCHANT_ID=tu_merchant_id
SECRET_KEY=tu_secret_key_yappy

# Configuración de administración
ADMIN_SECRET_KEY=tu_admin_secret_key

# Entorno de ejecución
NODE_ENV=development
