@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html,
  body {
    @apply dark:bg-primary-900 bg-primary-50;
  }
}

/* Estilos personalizados para el scroll de RunningPips - Oscuro y Dorado */
@layer components {
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #eab308 #334155;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #334155;
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #eab308 0%, #ca8a04 100%);
    border-radius: 10px;
    border: 1px solid #334155;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #ca8a04 0%, #a16207 100%);
  }

  /* Scroll para videos/PDFs/señales */
  .video-scroll-area {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 8px;
  }

  /* Ocultar completamente widgets externos */
  iframe[src*="tradingview"],
  iframe[src*="widget"],
  div[id*="tradingview"],
  div[class*="tradingview"],
  .tradingview-widget-container,
  [data-tradingview-widget],
  div[style*="position: absolute"][style*="bottom"],
  div[style*="position: fixed"][style*="bottom"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
}


