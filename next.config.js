/* 
module.exports = {
  async headers() {
    return [
      {
        // Evitar el caching para las rutas que empiezan con /api/ y /auth/
        source: '/api/(.*)',
        headers: [
          { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, proxy-revalidate' },
        ],
      },
      {
        // Evitar el caching para las rutas de autenticación de NextAuth
        source: '/auth/(.*)',
        headers: [
          { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, proxy-revalidate' },
        ],
      },
      {
        // Configurar el caching para el resto de las rutas
        source: '/(.*)',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=3600, s-maxage=86400' },
        ],
      },
    ];
  },
}; */
