// API de prueba para verificar que OpenAI funciona correctamente
export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const apiKey = process.env.OPENAI_API_KEY;
    
    if (!apiKey) {
      return res.status(400).json({
        success: false,
        message: "API key de OpenAI no configurada"
      });
    }

    if (!apiKey.startsWith('sk-')) {
      return res.status(400).json({
        success: false,
        message: "Formato de API key incorrecto"
      });
    }

    // Prueba simple con OpenAI
    const openaiResponse = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: "Responde solo: 'Germayori AI funcionando correctamente 🤖'"
          }
        ],
        max_tokens: 50,
        temperature: 0.1,
      }),
    });

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text();
      return res.status(openaiResponse.status).json({
        success: false,
        message: `Error ${openaiResponse.status}: ${openaiResponse.statusText}`,
        error: errorText
      });
    }

    const data = await openaiResponse.json();
    const response = data.choices[0]?.message?.content || "Sin respuesta";

    return res.status(200).json({
      success: true,
      message: "OpenAI funcionando correctamente",
      response: response,
      usage: data.usage
    });

  } catch (error) {
    console.error("Error en test OpenAI:", error);
    return res.status(500).json({
      success: false,
      message: "Error técnico",
      error: error.message
    });
  }
}
