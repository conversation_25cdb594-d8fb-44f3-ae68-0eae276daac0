/* import NextAuth from 'next-auth';
import UsuarioAcademy from '../../../models/userAcademyModel';
import GoogleProvider from 'next-auth/providers/google';
import { MongoDBAdapter } from "@auth/mongodb-adapter"
import clientPromise from "../../../lib/mongodb";
import connectDB from "../../../lib/mongoose";

export default NextAuth({
  adapter: MongoDBAdapter(clientPromise),
  secret: process.env.SECRET,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
  ],
  callbacks: {
    async signIn(user, account, profile) {
      const currentUser = user.user
      try {
        await connectDB();
        // Verificar si el usuario ya existe en la base de datos
        const existingUser = await UsuarioAcademy.findOne({ email: currentUser.email });
        if (!existingUser) {
          // Si el usuario no existe, gu<PERSON>rda<PERSON> en la base de datos
          await UsuarioAcademy.create({
            name: currentUser.name,
            email: currentUser.email,
            created: new Date()
            // Otras propiedades según tus necesidades
          });
        } else {
          console.log('existe el usuario')
          // Actualizar la información del usuario si es necesario
          // Puedes implementar la lógica para actualizar isActive, cutoffDate, canEnter, etc.
        }
      } catch (error) {
        console.error('Error al guardar/actualizar el usuario en la base de datos:', error);
        return Promise.resolve(false); 
      }
      return Promise.resolve(true);
    },
  },
}); */
import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import { MongoDBAdapter } from '@auth/mongodb-adapter';
import clientPromise from '../../../lib/mongodb.js';
import connectDB from '../../../lib/mongoose.js';
import UsuarioAcademy from '../../../models/userAcademyModel.js';

export default NextAuth({
  //adapter: MongoDBAdapter(clientPromise),
  secret: process.env.SECRET,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
  ],
  pages: {
    error: '/login', // Redirigir errores a login
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        await connectDB();
        const existingUser = await UsuarioAcademy.findOne({ email: user.email });

        if (!existingUser) {
          // Crear usuario pero SIN acceso por defecto
          await UsuarioAcademy.create({
            name: user.name,
            email: user.email,
            created: new Date(),
            canEnter: false, // IMPORTANTE: Sin acceso hasta que pague
            isActive: false, // Inactivo hasta que pague
            role: 'PENDIENTE' // Rol pendiente hasta verificar pago
          });
          console.log('Usuario creado sin acceso - debe pagar para activar');
        } else {
          console.log('El usuario ya existe');
          // Verificar si el usuario tiene acceso
          if (!existingUser.canEnter) {
            console.log('Usuario sin acceso activo');
          }
        }
      } catch (error) {
        console.error('Error al guardar/actualizar el usuario en la base de datos:', error);
        return false;
      }
      return true; // Permitir login pero el acceso se controla en PrivateRoute
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
        session.user.email = token.email;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
      }
      return token;
    },
  },
});
