import AWS from "aws-sdk";
import UsuarioAcademy from "../../models/userAcademyModel";
const s3 = new AWS.S3();

export default async function handler(req, res) {
  const user = req.query.user;
  const bucketName = "bucket-rp";
  const directoryAWS = [];
  const currentUser = await UsuarioAcademy.findOne({ email: user });

  try {
    const paramsTest = {
      Bucket: bucketName,
      Delimiter: "/",
    };
    const data = await s3.listObjectsV2(paramsTest).promise();

    if (data.CommonPrefixes) {
      for (const commonPrefix of data.CommonPrefixes) {
        directoryAWS.push(commonPrefix.Prefix);
      }
      const mainVideos = {};
      for (const directory of directoryAWS) {
        const directoryParams = {
          Bucket: bucketName,
          Prefix: directory,
        };
        const directoryData = await s3.listObjectsV2(directoryParams).promise();
        const videos = [];
        for (const file of directoryData.Contents) {
          const nameVideo = file.Key;

          // Saltar el objeto si es una carpeta (termina con '/')
          if (!nameVideo.endsWith("/")) {
            const url = s3.getSignedUrl("getObject", {
              Bucket: bucketName,
              Key: nameVideo,
              Expires: 60 * 60, // URL válida por una hora
            });
            videos.push({ name: nameVideo, url: url });
          }
        }
        if (videos.length > 0) {
          mainVideos[directory] = videos;
        }
      }
      const entradas = Object.entries(mainVideos);
      // Filtrar las entradas por clave
      /* const filtradas = entradas.filter(([clave, valor]) =>
        clave.toLowerCase().includes(currentUser.role.toLowerCase()) || clave.toLowerCase().includes("señales de trading/")
      ); */
      // Reconstruir el objeto filtrado
      const datosFiltrados = Object.fromEntries(entradas);
      // Definir el orden deseado
      const orden = ["Trading Basico/", "Trading Intermedio/", "Trading Avanzado/"];

      // Ordenar el objeto según el nivel
      const nivelesOrdenados = Object.keys(datosFiltrados)
        .sort((a, b) => orden.indexOf(a) - orden.indexOf(b))
        .reduce((obj, key) => {
          obj[key] = datosFiltrados[key];
          return obj;
        }, {});
      res.status(200).json(nivelesOrdenados);
    } else {
      res.status(200).json([]);
    }
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error al listar los videos", details: error.message });
  }
}
