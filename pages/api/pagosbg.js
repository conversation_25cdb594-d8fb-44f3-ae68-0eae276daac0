import * as yappy from "yappy-node-back-sdk";
import UsuarioAcademy from "../../models/userAcademyModel.js";
import generateUniqueId from "../../utils/generateIdPayment";
import connectDB from "../../lib/mongoose.js";
const MERCHANT_ID = process.env.MERCHANT_ID;
const SECRET_KEY = process.env.SECRET_KEY;
console.log(MERCHANT_ID, SECRET_KEY);
const yappyClient = yappy.createClient(MERCHANT_ID, SECRET_KEY);

const yappyEndpoint = async (req, res) => {
  if (req.method === "GET") {
    await connectDB();
    const { payment: subtotal, customer: customer } = req.query;
    if (subtotal) {
      const uniqueId = generateUniqueId();
      const user = await UsuarioAcademy.findOneAndUpdate(
        { email: customer },
        { paymentId: uniqueId },
        { new: true } // Devolver el documento actualizado
      );
      console.log(user, subtotal);
      console.log("Calculating payment details...");
      const subtotalNum = parseFloat(subtotal);
      const taxes = subtotalNum * 0.07;
      const total = subtotalNum + taxes;
      const payment = {
        total: total.toFixed(2), // Formatear total a 2 decimales
        subtotal: subtotalNum.toFixed(2), // Asegurar que el subtotal también tenga 2 decimales
        shipping: 0.0,
        discount: 0.0,
        taxes: taxes.toFixed(2), // Formatear taxes a 2 decimales
        orderId: uniqueId.toString(), // prueba
        successUrl: "https://runningpips.com/",
        failUrl: "https://runningpips.com/",
        tel: "", // el usuario coloca su telefono manualmente en este caso
        domain: "https://runningpips.com/",
      };
      console.log("Payment details:", payment);

      const response = await yappyClient.getPaymentUrl(payment);
      console.log(response);
      if (!response.success) {
        res.status(500).json(response);
        console.log("sin pago , continua a validar hash");
      } else {
        res.json(response);
      }
    } else if (subtotal === undefined) {
      const success = yappyClient.validateHash(req.query);
      const { orderId } = req.query;
      console.log(req.query)
      console.log(orderId);
      if (success === true) {
        const currentDate = new Date();
        currentDate.setDate(currentDate.getDate() + 30);
        const updatedUser = await UsuarioAcademy.findOneAndUpdate(
          { paymentId: orderId },
          {
            $set: {
              cutoffDate: currentDate,
              canEnter: true,
              isActive: true,
            },
          },
          { new: true }
        );
        console.log("Usuario actualizado:", updatedUser);
        return res.status(200).json({
          success: true,
          customer: customer,
          message: "El usuario pagó",
        });
      } else if (success === false) {
        console.log("el usuario no pago");
        return res.status(200).json({
          success: false,
          customer: customer,
          message: "El usuario no pago",
        });
      } else {
        console.log("Validación fallida");
        return res
          .status(400)
          .json({ success: false, message: "Validación fallida" });
      }
    }
  }
};

export default yappyEndpoint;
