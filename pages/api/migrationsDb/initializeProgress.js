// pages/api/migrationsDb/initializeProgress.js
import UsuarioAcademy from "../../../models/userAcademyModel.js";
import UserProgress from "../../../models/userProgressModel.js";
import connectDB from "../../../lib/mongoose.js";

const initializeUserProgress = async (req, res) => {
  try {
    await connectDB();
    
    // Obtener todos los usuarios de la academia
    const academyUsers = await UsuarioAcademy.find({});
    
    let initializedCount = 0;
    let existingCount = 0;
    
    for (const user of academyUsers) {
      // Verificar si ya existe progreso para este usuario
      const existingProgress = await UserProgress.findOne({ userEmail: user.email });
      
      if (!existingProgress) {
        // Crear progreso inicial
        const newProgress = new UserProgress({
          userEmail: user.email,
          startDate: user.created ? new Date(user.created) : new Date(),
          lastActivity: new Date(),
          stats: {
            totalLogins: 1,
            lastLoginDate: new Date(),
            loginStreak: 1,
            averageSessionTime: 0
          },
          categoryProgress: {
            basico: { completed: 0, total: 0 },
            intermedio: { completed: 0, total: 0 },
            avanzado: { completed: 0, total: 0 }
          },
          overallProgress: 0
        });
        
        await newProgress.save();
        initializedCount++;
        console.log(`Progreso inicializado para: ${user.email}`);
      } else {
        existingCount++;
        console.log(`Progreso ya existe para: ${user.email}`);
      }
    }
    
    const result = {
      message: "Inicialización de progreso completada",
      totalUsers: academyUsers.length,
      initialized: initializedCount,
      existing: existingCount
    };
    
    console.log(result);
    
    if (res) {
      res.status(200).json(result);
    } else {
      return result;
    }
    
  } catch (error) {
    console.error('Error durante la inicialización de progreso:', error);
    
    const errorResult = {
      message: "Error durante la inicialización",
      error: error.message
    };
    
    if (res) {
      res.status(500).json(errorResult);
    } else {
      throw error;
    }
  }
};

export default initializeUserProgress;
