// pages/api/certificate.js
import UserProgress from '../../models/userProgressModel.js';
import UsuarioAcademy from '../../models/userAcademyModel.js';
import connectDB from '../../lib/mongoose.js';

export default async function handler(req, res) {
  await connectDB();
  
  const { method } = req;
  const { userEmail } = req.query;

  if (!userEmail) {
    return res.status(400).json({ error: 'Email de usuario requerido' });
  }

  try {
    switch (method) {
      case 'GET':
        // Verificar si el usuario puede obtener certificado
        const userProgress = await UserProgress.findOne({ userEmail });
        const academyUser = await UsuarioAcademy.findOne({ email: userEmail });
        
        if (!userProgress || !academyUser) {
          return res.status(404).json({ error: 'Usuario no encontrado' });
        }
        
        // Calcular progreso
        userProgress.calculateOverallProgress();
        
        const canGetCertificate = userProgress.overallProgress >= 100;
        
        if (!canGetCertificate) {
          return res.status(400).json({ 
            error: 'Curso no completado',
            progress: userProgress.overallProgress,
            message: `Necesitas completar el ${100 - userProgress.overallProgress}% restante para obtener tu certificado`
          });
        }
        
        // Verificar si ya tiene certificado
        const existingCertificate = userProgress.certificates.find(cert => cert.type === 'completo');
        
        if (existingCertificate) {
          return res.status(200).json({
            success: true,
            certificate: existingCertificate,
            message: 'Certificado ya emitido'
          });
        }
        
        // Generar nuevo certificado
        const certificateId = `RP-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
        
        const newCertificate = {
          type: 'completo',
          issuedAt: new Date(),
          certificateId: certificateId,
          validatedBy: 'Germayori - La Diosa del Trading'
        };
        
        userProgress.certificates.push(newCertificate);
        await userProgress.save();
        
        res.status(200).json({
          success: true,
          certificate: newCertificate,
          message: 'Certificado generado exitosamente'
        });
        break;

      case 'POST':
        // Generar certificado PDF (placeholder - aquí podrías integrar una librería de PDF)
        const { certificateId: requestedCertificateId } = req.body;
        
        const progress = await UserProgress.findOne({ userEmail });
        const user = await UsuarioAcademy.findOne({ email: userEmail });
        
        if (!progress || !user) {
          return res.status(404).json({ error: 'Usuario no encontrado' });
        }
        
        const certificate = progress.certificates.find(cert => cert.certificateId === requestedCertificateId);
        
        if (!certificate) {
          return res.status(404).json({ error: 'Certificado no encontrado' });
        }
        
        // Aquí podrías generar un PDF real usando librerías como jsPDF o PDFKit
        const certificateData = {
          studentName: user.name,
          studentEmail: user.email,
          certificateId: certificate.certificateId,
          issuedDate: certificate.issuedAt.toLocaleDateString('es-ES', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          }),
          validatedBy: certificate.validatedBy,
          courseName: 'RunningPips Academy - Curso Completo de Trading Institucional',
          institution: 'RunningPips Academy',
          totalHours: Math.round(progress.totalStudyTime / 60),
          videosCompleted: progress.videosWatched.filter(v => v.progress >= 80).length,
          overallProgress: progress.overallProgress
        };
        
        res.status(200).json({
          success: true,
          certificateData,
          downloadUrl: `/api/certificate/download?certificateId=${certificateId}&userEmail=${userEmail}`,
          message: 'Datos del certificado obtenidos'
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Error en API de certificados:', error);
    res.status(500).json({ error: 'Error interno del servidor', details: error.message });
  }
}
