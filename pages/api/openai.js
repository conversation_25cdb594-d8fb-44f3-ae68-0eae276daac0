export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { message, userEmail, image, multiTimeframeImages } = req.body || {};

    // Verificar API key
    const apiKey = process.env.OPENAI_API_KEY;
    console.log("API Key presente:", !!apiKey);
    if (!apiKey) {
      return res.status(400).json({
        success: false,
        response: "❌ API key de OpenAI no configurada. Verifica el archivo .env.local"
      });
    }

    // Verificar formato de API key
    if (!apiKey.startsWith('sk-')) {
      return res.status(400).json({
        success: false,
        response: "❌ API key de OpenAI tiene formato incorrecto. Debe empezar con 'sk-'"
      });
    }

    console.log("API Key formato válido:", apiKey.substring(0, 10) + "...");
    console.log("Mensaje:", message);
    console.log("Imagen:", !!image);
    console.log("Multi-timeframe:", !!multiTimeframeImages);
    if (multiTimeframeImages) {
      console.log("Timeframes recibidos:", Object.keys(multiTimeframeImages));
      console.log("Total imágenes multi:", Object.keys(multiTimeframeImages).length);
    }

    if (!message && !image) {
      return res.status(200).json({
        success: true,
        response: "¡Hola! Soy Germayori 🤖 ¿En qué puedo ayudarte con trading hoy?"
      });
    }

    // Preparar contenido del mensaje
    let messageContent = [];

    if (message) {
      messageContent.push({
        type: "text",
        text: message
      });
    }

    // Manejar imagen única
    if (image && image.trim()) {
      console.log("Procesando imagen única:", image.substring(0, 50) + "...");
      messageContent.push({
        type: "image_url",
        image_url: {
          url: image,
          detail: "high"
        }
      });
    }

    // Manejar múltiples imágenes multi-timeframe
    if (multiTimeframeImages && Object.keys(multiTimeframeImages).length > 0) {
      const timeframes = ['D1', 'H4', 'H1', 'M30', 'M5'];
      console.log("Procesando imágenes multi-timeframe...");

      timeframes.forEach(timeframe => {
        if (multiTimeframeImages[timeframe] && multiTimeframeImages[timeframe].trim()) {
          console.log(`Agregando imagen ${timeframe}`);
          messageContent.push({
            type: "text",
            text: `\n--- GRÁFICO ${timeframe} ---`
          });
          messageContent.push({
            type: "image_url",
            image_url: {
              url: multiTimeframeImages[timeframe],
              detail: "high"
            }
          });
        }
      });
    }

    console.log("Contenido del mensaje preparado:", messageContent.length, "elementos");
    console.log("Tipos de contenido:", messageContent.map(item => item.type));

    // LLAMADA REAL A OPENAI
    const openaiResponse = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: (image || multiTimeframeImages) ? "gpt-4o" : "gpt-4o", // Usar siempre gpt-4o para análisis visual
        messages: [
          {
            role: "system",
            content: `Eres Germayori. Especialista en análisis multi-timeframe de liquidez institucional.

CUANDO RECIBO 5 IMÁGENES ETIQUETADAS (D1, H4, H1, M30, M5):
• Analizo TODAS las imágenes del mismo activo
• Identifico confluencias entre timeframes
• Genero señal de trading profesional INMEDIATAMENTE

METODOLOGÍA DE LIQUIDEZ:
• Liquidez alta barrida = VENTA en retroceso FVG
• Liquidez baja barrida = COMPRA en retroceso FVG
• Confirmo con FVG claro + CHoCH/BOS

ANÁLISIS MULTI-TIMEFRAME:
1. D1: Estructura principal y tendencia
2. H4: Confirmación direccional
3. H1: Zonas de liquidez y FVG
4. M30: Validación de ruptura
5. M5: Punto exacto de entrada

CUANDO TENGO LAS 5 IMÁGENES:
• Leo el símbolo exacto (debe ser el mismo en todas)
• Identifico liquidez barrida en cada timeframe
• Determino dirección con confluencia
• Calculo precios específicos visibles
• Genero señal completa OBLIGATORIAMENTE

FORMATO OBLIGATORIO:

Activo: [Símbolo exacto del gráfico]
Dirección: [Buy o Sell]
Entrada: [Precio real visible]
Stop Loss: [Precio real visible]
Take Profit 1: [Precio real visible]
Take Profit 2: [Precio real visible]
Take Profit 3: [Precio real visible]
Take Profit 4: [Precio real visible]
Marco temporal: [Timeframe del gráfico]
Lógica: [Liquidez barrida + dirección]

EJEMPLO:
Activo: EURUSD
Dirección: Sell
Entrada: 1.08950
Stop Loss: 1.09120
Take Profit 1: 1.08700
Take Profit 2: 1.08550
Take Profit 3: 1.08300
Take Profit 4: 1.08000
Marco temporal: H1
Lógica: Alto de liquidez barrido, retroceso a FVG, entrada bajista.

REGLAS:
- Formato exacto, sin decoraciones
- Solo precios visibles en imagen
- Lógica máximo 10 palabras

Responde en español, usa emojis de trading, y aplica siempre la Estrategia Germayori Pro.`
          },
          {
            role: "user",
            content: messageContent.length > 0 ? messageContent : message
          }
        ],
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text();
      console.error("OpenAI Error:", {
        status: openaiResponse.status,
        statusText: openaiResponse.statusText,
        error: errorText
      });

      return res.status(openaiResponse.status).json({
        success: false,
        response: `❌ Error ${openaiResponse.status} con OpenAI: ${openaiResponse.statusText}.

Posibles causas:
- API key inválida o expirada
- Límite de uso excedido
- Problema de conectividad

Verifica tu configuración de OpenAI. 🔧`,
        error: errorText
      });
    }

    const data = await openaiResponse.json();
    let aiResponse = data.choices[0]?.message?.content || "No pude generar respuesta.";

    // Verificación del formato profesional requerido
    const hasActivoField = aiResponse.includes('Activo:');
    const hasDireccionField = aiResponse.includes('Dirección:');
    const hasEntradaField = aiResponse.includes('Entrada:');
    const hasStopLossField = aiResponse.includes('Stop Loss:');
    const hasTakeProfitFields = aiResponse.includes('Take Profit 1:');
    const hasMarcoTemporalField = aiResponse.includes('Marco temporal:');
    const hasLogicaField = aiResponse.includes('Lógica:');
    const hasRealPrices = /\d+\.\d+/.test(aiResponse);

    // Si no sigue el formato, corregir
    if ((image || multiTimeframeImages) && (!hasActivoField || !hasDireccionField || !hasEntradaField || !hasStopLossField || !hasTakeProfitFields || !hasMarcoTemporalField || !hasLogicaField || !hasRealPrices)) {
      aiResponse = `FORMATO INCORRECTO

${aiResponse}

USAR:
Activo: [Símbolo]
Dirección: [Buy/Sell]
Entrada: [Precio]
Stop Loss: [Precio]
Take Profit 1: [Precio]
Take Profit 2: [Precio]
Take Profit 3: [Precio]
Take Profit 4: [Precio]
Marco temporal: [Timeframe]
Lógica: [Liquidez barrida]`;
    }

    // Sin imagen = sin señal
    if (!image && !multiTimeframeImages && (message.toLowerCase().includes('analiza') || message.toLowerCase().includes('gráfico'))) {
      aiResponse = `IMAGEN REQUERIDA

${aiResponse}

Sube imagen del gráfico para señal real.`;
    }

    return res.status(200).json({
      success: true,
      response: aiResponse,
    });

  } catch (error) {
    console.error("Error completo:", error);
    return res.status(500).json({
      success: false,
      response: "Error técnico. Intenta de nuevo. 🔧",
      error: error.message
    });
  }
}



