import React, { useState, useRef, useEffect } from "react";
import PrivateRoute from "../components/privateRoute";
import { useSession } from "next-auth/react";

const GermayoriPage = () => {
  const { data: session } = useSession();
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  // Estados para las 5 imágenes multi-timeframe
  const [timeframeImages, setTimeframeImages] = useState({
    D1: { file: null, preview: null },
    H4: { file: null, preview: null },
    H1: { file: null, preview: null },
    M30: { file: null, preview: null },
    M5: { file: null, preview: null }
  });
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Mensaje inicial de Germayori
  useEffect(() => {
    const initialMessage = {
      role: "assistant",
      content: `¡Hola! Soy Germayori, especialista en análisis de liquidez institucional.

🎯 **ANÁLISIS MULTI-TIMEFRAME PROFESIONAL**

Usa el panel de arriba para subir 5 imágenes del MISMO activo:

📊 **TIMEFRAMES REQUERIDOS:**
• **D1** - Estructura principal y tendencia
• **H4** - Confirmación de dirección
• **H1** - Zonas de liquidez y FVG
• **M30** - Validación de ruptura
• **M5** - Punto exacto de entrada

⚠️ **INSTRUCCIONES:**
1. Sube las 5 imágenes del mismo par (ej: XAUUSD)
2. Haz clic en "Generar Señal de Trading"
3. Recibirás análisis profesional con formato:

Activo: [PAR]
Dirección: [Buy/Sell]
Entrada: [Precio]
Stop Loss: [Precio]
Take Profit 1-4: [Precios]
Marco temporal: [Confluencia]
Lógica: [Análisis de liquidez]

🚀 **¡Comienza subiendo las imágenes arriba!**`,
      timestamp: new Date().toLocaleTimeString()
    };

    setMessages([initialMessage]);
  }, []);

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
  };

  // Función para subir imagen por timeframe específico
  const handleTimeframeImageUpload = (timeframe, e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setTimeframeImages(prev => ({
          ...prev,
          [timeframe]: {
            file: file,
            preview: e.target.result
          }
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  // Función para remover imagen de timeframe específico
  const removeTimeframeImage = (timeframe) => {
    setTimeframeImages(prev => ({
      ...prev,
      [timeframe]: { file: null, preview: null }
    }));
  };

  // Verificar si todas las imágenes están subidas
  const allImagesUploaded = Object.values(timeframeImages).every(img => img.file !== null);

  // Función para analizar las 5 imágenes multi-timeframe
  const handleAnalyzeMultiTimeframe = async () => {
    if (!allImagesUploaded) {
      alert("Por favor sube las 5 imágenes antes de analizar");
      return;
    }

    setIsLoading(true);

    // Crear mensaje con las 5 imágenes
    const userMessage = {
      role: "user",
      content: "Analiza estas 5 imágenes multi-timeframe del mismo activo y proporciona señal de trading:",
      multiTimeframeImages: timeframeImages,
      hasMultiTimeframe: true
    };
    setMessages(prev => [...prev, userMessage]);

    try {
      // Preparar las imágenes para envío
      const imagesData = {};
      Object.keys(timeframeImages).forEach(timeframe => {
        if (timeframeImages[timeframe].preview) {
          imagesData[timeframe] = timeframeImages[timeframe].preview;
        }
      });

      console.log("Enviando imágenes:", Object.keys(imagesData));
      console.log("Total imágenes:", Object.keys(imagesData).length);

      const response = await fetch("/api/openai", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: `Análisis multi-timeframe: D1, H4, H1, M30, M5 del mismo activo. Proporciona señal de trading profesional.`,
          userEmail: session?.user?.email,
          multiTimeframeImages: imagesData,
        }),
      });

      const data = await response.json();

      if (data.success) {
        const aiMessage = {
          role: "assistant",
          content: data.response,
          isSignal: true
        };
        setMessages(prev => [...prev, aiMessage]);
      } else {
        const errorMessage = {
          role: "assistant",
          content: "Error procesando análisis multi-timeframe. Intenta de nuevo."
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      const errorMessage = {
        role: "assistant",
        content: "Error de conexión en análisis multi-timeframe."
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() && !selectedImage) return;

    const userMessage = {
      role: "user",
      content: inputMessage,
      image: imagePreview,
      hasImage: !!selectedImage
    };
    setMessages(prev => [...prev, userMessage]);

    const currentMessage = inputMessage;
    setInputMessage("");
    setSelectedImage(null);
    setImagePreview(null);
    setIsLoading(true);

    try {
      const response = await fetch("/api/openai", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: currentMessage,
          userEmail: session?.user?.email,
          image: imagePreview,
        }),
      });

      const data = await response.json();

      if (data.success) {
        const aiMessage = { role: "assistant", content: data.response };
        setMessages(prev => [...prev, aiMessage]);
      } else {
        const errorMessage = {
          role: "assistant",
          content: "Lo siento, hubo un problema procesando tu mensaje. 🔧"
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      const errorMessage = {
        role: "assistant",
        content: "Error de conexión. Intenta de nuevo. 🌐"
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startChat = () => {
    setInputMessage("Hola Germayori, ¿estás lista para ayudarme con trading?");
    setTimeout(() => {
      handleSendMessage();
    }, 100);
  };

  return (
    <PrivateRoute>
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
        {/* Header Superior */}
        <div className="bg-black/30 backdrop-blur-sm border-b border-white/10">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Botón Regresar a Inicio */}
                <button
                  onClick={() => window.location.href = '/'}
                  className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 transition-all duration-200 text-secondary-200 hover:text-secondary-100"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span className="text-sm font-medium">Regresar a Inicio</span>
                </button>

                <div className="w-12 h-12 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-secondary-400">Germayori AI</h1>
                  <p className="text-secondary-200 text-sm">Estrategia Pro - Liquidez Institucional</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-secondary-200 text-sm">Usuario: {session?.user?.name || session?.user?.email}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-xs font-medium">En línea</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contenedor Principal */}
        <div className="flex flex-col h-[calc(100vh-80px)]">
          {/* Área de Mensajes */}
          <div className="flex-1 overflow-y-auto p-6 custom-scrollbar">
            <div className="max-w-6xl mx-auto">
              {messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full min-h-[500px]">
                  <div className="bg-gradient-to-r from-secondary-500/20 to-primary-500/20 rounded-3xl p-12 border border-white/10 max-w-4xl">
                    <div className="text-center">
                      <div className="w-24 h-24 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-full flex items-center justify-center mx-auto mb-8">
                        <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      </div>
                      <h2 className="text-4xl font-bold text-secondary-400 mb-6">¡Bienvenido a Germayori AI!</h2>
                      <p className="text-secondary-200 text-xl leading-relaxed mb-8">
                        Tu mentora especializada en <span className="text-secondary-400 font-semibold">Smart Money Concepts</span><br/>
                        Creadora de la <span className="text-secondary-400 font-semibold">✨ Estrategia Germayori Pro ✨</span>
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                          <h3 className="text-secondary-400 font-bold text-lg mb-3">📊 Análisis Técnico</h3>
                          <p className="text-secondary-200">FVG, Order Blocks, CHoCH/BOS, Liquidez Institucional</p>
                        </div>
                        <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                          <h3 className="text-secondary-400 font-bold text-lg mb-3">📈 Análisis de Gráficos</h3>
                          <p className="text-secondary-200">Sube imágenes de gráficos para análisis detallado</p>
                        </div>
                        <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                          <h3 className="text-secondary-400 font-bold text-lg mb-3">⚡ Señales en Tiempo Real</h3>
                          <p className="text-secondary-200">Identificación de oportunidades de compra y venta</p>
                        </div>
                        <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                          <h3 className="text-secondary-400 font-bold text-lg mb-3">🎯 Multi-Timeframe</h3>
                          <p className="text-secondary-200">Análisis en Daily, H1, M30 y M5</p>
                        </div>
                      </div>

                      <button
                        onClick={startChat}
                        className="bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
                      >
                        🚀 Comenzar Chat con Germayori
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Panel de Análisis Multi-Timeframe */}
                  <div className="bg-gradient-to-r from-secondary-500/20 to-secondary-600/20 border border-secondary-500/30 rounded-xl p-6 mb-6">
                    <h3 className="text-secondary-400 font-bold text-xl mb-4 text-center">
                      📊 Análisis Multi-Timeframe - Sube las 5 Imágenes
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                      {['D1', 'H4', 'H1', 'M30', 'M5'].map((timeframe) => (
                        <div key={timeframe} className="bg-black/40 border border-white/20 rounded-lg p-4">
                          <h4 className="text-secondary-400 font-semibold text-center mb-3">{timeframe}</h4>

                          {timeframeImages[timeframe].preview ? (
                            <div className="relative">
                              <img
                                src={timeframeImages[timeframe].preview}
                                alt={`Gráfico ${timeframe}`}
                                className="w-full h-32 object-cover rounded-lg border border-white/30"
                              />
                              <button
                                onClick={() => removeTimeframeImage(timeframe)}
                                className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm"
                              >
                                ×
                              </button>
                            </div>
                          ) : (
                            <label className="cursor-pointer">
                              <div className="w-full h-32 border-2 border-dashed border-secondary-400/50 rounded-lg flex flex-col items-center justify-center hover:border-secondary-400 transition-colors">
                                <svg className="w-8 h-8 text-secondary-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                <span className="text-secondary-300 text-sm">Subir {timeframe}</span>
                              </div>
                              <input
                                type="file"
                                accept="image/*"
                                onChange={(e) => handleTimeframeImageUpload(timeframe, e)}
                                className="hidden"
                              />
                            </label>
                          )}
                        </div>
                      ))}
                    </div>

                    <div className="text-center space-y-3">
                      <button
                        onClick={handleAnalyzeMultiTimeframe}
                        disabled={!allImagesUploaded || isLoading}
                        className={`px-8 py-3 rounded-xl font-semibold transition-all duration-200 ${
                          allImagesUploaded && !isLoading
                            ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg'
                            : 'bg-gray-500/50 text-gray-300 cursor-not-allowed'
                        }`}
                      >
                        {isLoading ? 'Analizando...' : allImagesUploaded ? '🎯 Generar Señal de Trading' : `Faltan ${5 - Object.values(timeframeImages).filter(img => img.file).length} imágenes`}
                      </button>

                      {/* Debug: Mostrar estado de imágenes */}
                      <div className="text-xs text-secondary-300">
                        Debug: {Object.entries(timeframeImages).map(([tf, img]) => `${tf}:${img.file ? '✅' : '❌'}`).join(' | ')}
                      </div>
                    </div>
                  </div>
                  {messages.map((message, index) => (
                    <div
                      key={index}
                      className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                    >
                      <div
                        className={`max-w-2xl px-6 py-4 rounded-2xl ${
                          message.role === "user"
                            ? "bg-gradient-to-r from-secondary-500 to-secondary-600 text-white"
                            : "bg-white/10 text-secondary-100 border border-white/20"
                        }`}
                      >
                        {message.hasImage && message.image && (
                          <div className="mb-4">
                            <img
                              src={message.image}
                              alt="Imagen enviada"
                              className="max-w-full max-h-96 rounded-lg border border-white/30"
                            />
                          </div>
                        )}
                        {message.hasMultiTimeframe && message.multiTimeframeImages && (
                          <div className="mb-4">
                            <div className="text-secondary-300 text-sm mb-2">📊 Análisis Multi-Timeframe:</div>
                            <div className="grid grid-cols-5 gap-2">
                              {['D1', 'H4', 'H1', 'M30', 'M5'].map(timeframe => (
                                message.multiTimeframeImages[timeframe] && (
                                  <div key={timeframe} className="text-center">
                                    <div className="text-secondary-400 text-xs mb-1">{timeframe}</div>
                                    <img
                                      src={message.multiTimeframeImages[timeframe]}
                                      alt={`Gráfico ${timeframe}`}
                                      className="w-full h-20 object-cover rounded border border-white/30"
                                    />
                                  </div>
                                )
                              ))}
                            </div>
                          </div>
                        )}
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-white/10 border border-white/20 rounded-2xl px-6 py-4">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                          <span className="text-secondary-200 ml-2">Germayori está pensando...</span>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>
          </div>

          {/* Área de Input */}
          <div className="bg-black/30 backdrop-blur-sm border-t border-white/10 p-6">
            <div className="max-w-6xl mx-auto">
              <div className="flex flex-col space-y-4">
                {/* Preview de imagen */}
                {imagePreview && (
                  <div className="flex items-center space-x-4 bg-white/10 rounded-xl p-4 border border-white/20">
                    <img src={imagePreview} alt="Preview" className="w-16 h-16 rounded-lg object-cover" />
                    <span className="text-secondary-200 flex-1">Imagen seleccionada</span>
                    <button
                      onClick={removeImage}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                )}

                <div className="flex space-x-4">
                  <div className="flex-1">
                    <textarea
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Pregunta sobre trading, análisis técnico o sube una imagen de gráfico..."
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-6 py-4 text-secondary-100 placeholder-secondary-300 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-transparent resize-none"
                      rows="3"
                      disabled={isLoading}
                    />
                  </div>
                  
                  <div className="flex flex-col space-y-2">
                    {/* Botón subir imagen */}
                    <label className="bg-white/10 hover:bg-white/20 border border-white/20 text-secondary-200 px-4 py-2 rounded-xl cursor-pointer transition-colors duration-200 flex items-center justify-center">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        disabled={isLoading}
                      />
                    </label>

                    {/* Botón enviar */}
                    <button
                      onClick={handleSendMessage}
                      disabled={isLoading || (!inputMessage.trim() && !selectedImage)}
                      className="bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 disabled:from-gray-600 disabled:to-gray-700 disabled:opacity-50 text-white px-4 py-2 rounded-xl font-medium transition-all duration-200 flex items-center justify-center"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <style jsx global>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(139, 92, 246, 0.1);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(251, 191, 36, 0.6);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(251, 191, 36, 0.8);
          }

          /* Ocultar widgets externos completamente */
          iframe[src*="tradingview"],
          iframe[src*="widget"],
          div[id*="tradingview"],
          div[class*="tradingview"],
          .tradingview-widget-container,
          [data-tradingview-widget],
          script[src*="tradingview"] + div,
          div[style*="position: fixed"][style*="bottom"],
          div[style*="position: absolute"][style*="bottom"] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
          }
        `}</style>
      </div>
    </PrivateRoute>
  );
};

export default GermayoriPage;
