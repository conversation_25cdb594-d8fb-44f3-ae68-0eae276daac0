import React, { useState, useEffect } from "react";
import PrivateRoute from "../components/privateRoute";
import { useSession } from "next-auth/react";
import Head from "next/head";

const NoticiasPage = () => {
  const { data: session } = useSession();
  const [selectedCategory, setSelectedCategory] = useState("todas");
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("noticias");
  const [noticias, setNoticias] = useState([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [error, setError] = useState(null);

  // Función para obtener noticias de la API
  const fetchNoticias = async () => {
    try {
      setError(null);
      const response = await fetch('/api/noticias');
      const data = await response.json();

      if (data.success) {
        setNoticias(data.noticias);
        setLastUpdate(new Date());
      } else {
        setError('Error al cargar noticias');
      }
    } catch (error) {
      console.error('Error fetching noticias:', error);
      setError('Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  // Efecto para cargar noticias al montar el componente
  useEffect(() => {
    fetchNoticias();
  }, []);

  // Efecto para actualizar noticias cada minuto
  useEffect(() => {
    const interval = setInterval(() => {
      fetchNoticias();
    }, 60000); // 60000ms = 1 minuto

    return () => clearInterval(interval);
  }, []);

  // Función para formatear la hora de última actualización
  const formatLastUpdate = () => {
    if (!lastUpdate) return '';
    return lastUpdate.toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const categorias = [
    { id: "todas", nombre: "Todas las Noticias", icono: "📰" },
    { id: "economia", nombre: "Economía", icono: "📊" },
    { id: "forex", nombre: "Forex", icono: "💱" },
    { id: "cripto", nombre: "Criptomonedas", icono: "₿" },
    { id: "materias-primas", nombre: "Materias Primas", icono: "🥇" },
    { id: "analisis", nombre: "Análisis Técnico", icono: "📈" }
  ];

  const filteredNoticias = noticias.filter(noticia => {
    const matchesCategory = selectedCategory === "todas" || noticia.categoria === selectedCategory;
    const matchesSearch = noticia.titulo.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         noticia.resumen.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const getImpactColor = (impacto) => {
    switch (impacto) {
      case "alto": return "bg-red-500/20 text-red-400 border-red-500/30";
      case "medio": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "bajo": return "bg-green-500/20 text-green-400 border-green-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <PrivateRoute>
      <Head>
        <title>Noticias y Calendario Económico - RunningPips</title>
      </Head>
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
        {/* Header */}
        <div className="bg-black/30 backdrop-blur-sm border-b border-white/10">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Botón Regresar a Inicio */}
                <button
                  onClick={() => window.location.href = '/'}
                  className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 transition-all duration-200 text-secondary-200 hover:text-secondary-100"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span className="text-sm font-medium">Regresar a Inicio</span>
                </button>
                
                <div className="w-12 h-12 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-secondary-400">Centro de Noticias</h1>
                  <p className="text-secondary-200 text-sm">
                    Noticias y Calendario Económico en Tiempo Real
                    {lastUpdate && (
                      <span className="block text-xs text-secondary-300 mt-1">
                        Última actualización: {formatLastUpdate()}
                      </span>
                    )}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-secondary-200 text-sm">Usuario: {session?.user?.name || session?.user?.email}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className={`w-2 h-2 rounded-full ${loading ? 'bg-yellow-400 animate-pulse' : 'bg-green-400 animate-pulse'}`}></div>
                  <span className={`text-xs font-medium ${loading ? 'text-yellow-400' : 'text-green-400'}`}>
                    {loading ? 'Actualizando...' : 'En línea'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contenido Principal */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Tabs de navegación */}
          <div className="mb-8">
            <div className="flex space-x-1 bg-white/10 rounded-xl p-1 mb-6">
              <button
                onClick={() => setActiveTab("noticias")}
                className={`flex-1 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2 ${
                  activeTab === "noticias"
                    ? "bg-secondary-500 text-white shadow-lg"
                    : "text-secondary-200 hover:text-secondary-100 hover:bg-white/10"
                }`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                </svg>
                <span>Noticias</span>
              </button>
              <button
                onClick={() => setActiveTab("calendario")}
                className={`flex-1 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2 ${
                  activeTab === "calendario"
                    ? "bg-secondary-500 text-white shadow-lg"
                    : "text-secondary-200 hover:text-secondary-100 hover:bg-white/10"
                }`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>Calendario Económico</span>
              </button>
            </div>
          </div>

          {/* Contenido de Noticias */}
          {activeTab === "noticias" && (
            <>
              {/* Barra de búsqueda y filtros */}
              <div className="mb-8">
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              {/* Buscador */}
              <div className="flex-1">
                <div className="relative">
                  <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <input
                    type="text"
                    placeholder="Buscar noticias..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-xl pl-10 pr-4 py-3 text-secondary-100 placeholder-secondary-300 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Categorías */}
            <div className="flex flex-wrap gap-3">
              {categorias.map((categoria) => (
                <button
                  key={categoria.id}
                  onClick={() => setSelectedCategory(categoria.id)}
                  className={`px-4 py-2 rounded-lg border transition-all duration-200 flex items-center space-x-2 ${
                    selectedCategory === categoria.id
                      ? "bg-secondary-500 border-secondary-500 text-white"
                      : "bg-white/10 border-white/20 text-secondary-200 hover:bg-white/20 hover:text-secondary-100"
                  }`}
                >
                  <span>{categoria.icono}</span>
                  <span className="text-sm font-medium">{categoria.nombre}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Indicador de carga */}
          {loading && noticias.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gradient-to-r from-secondary-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <svg className="w-8 h-8 text-secondary-300 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <p className="text-secondary-200">Cargando noticias en tiempo real...</p>
            </div>
          )}

          {/* Error de carga */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6 mb-6">
              <div className="flex items-center space-x-3">
                <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h3 className="text-red-400 font-semibold">Error al cargar noticias</h3>
                  <p className="text-red-300 text-sm">{error}</p>
                </div>
                <button
                  onClick={fetchNoticias}
                  className="ml-auto px-4 py-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 rounded-lg text-red-400 text-sm transition-colors"
                >
                  Reintentar
                </button>
              </div>
            </div>
          )}

          {/* Grid de Noticias */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredNoticias.map((noticia) => (
              <div
                key={noticia.id}
                className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl overflow-hidden hover:bg-white/15 transition-all duration-300 hover:scale-105 hover:shadow-xl"
              >
                {/* Imagen */}
                <div className="h-48 bg-gradient-to-r from-secondary-500/20 to-primary-500/20 flex items-center justify-center">
                  <svg className="w-16 h-16 text-secondary-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                  </svg>
                </div>

                {/* Contenido */}
                <div className="p-6">
                  {/* Header de la noticia */}
                  <div className="flex items-center justify-between mb-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getImpactColor(noticia.impacto)}`}>
                      Impacto {noticia.impacto}
                    </span>
                    <span className="text-secondary-300 text-xs">{noticia.fuente}</span>
                  </div>

                  {/* Título */}
                  <h3 className="text-secondary-100 font-bold text-lg mb-3 line-clamp-2 leading-tight">
                    {noticia.titulo}
                  </h3>

                  {/* Resumen */}
                  <p className="text-secondary-200 text-sm mb-4 line-clamp-3 leading-relaxed">
                    {noticia.resumen}
                  </p>

                  {/* Footer */}
                  <div className="flex items-center justify-between">
                    <span className="text-secondary-300 text-xs">
                      {formatDate(noticia.fecha)}
                    </span>
                    <a
                      href={noticia.url || '#'}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-secondary-400 hover:text-secondary-300 transition-colors"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Mensaje cuando no hay resultados */}
          {filteredNoticias.length === 0 && (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gradient-to-r from-secondary-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-secondary-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-secondary-200 text-xl font-semibold mb-2">No se encontraron noticias</h3>
              <p className="text-secondary-300">Intenta cambiar los filtros o el término de búsqueda</p>
            </div>
          )}
          </>
          )}

          {/* Contenido del Calendario Económico */}
          {activeTab === "calendario" && (
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl overflow-hidden">
              <div className="p-6 border-b border-white/10">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-secondary-400">Calendario Económico</h2>
                    <p className="text-secondary-200 text-sm">Eventos económicos importantes que pueden impactar los mercados</p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-8 text-center" style={{ minHeight: '400px' }}>
                  <div className="flex flex-col items-center justify-center h-full space-y-6">
                    <div className="w-20 h-20 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>

                    <div className="text-center">
                      <h3 className="text-2xl font-bold text-secondary-400 mb-3">Calendario Económico Myfxbook</h3>
                      <p className="text-secondary-200 mb-6 max-w-md">
                        Accede al calendario económico completo de Myfxbook para ver todos los eventos importantes que pueden impactar los mercados.
                      </p>
                    </div>

                    <a
                      href="https://www.myfxbook.com/forex-economic-calendar"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                      <span>Abrir Calendario Económico</span>
                    </a>

                    <div className="flex items-center space-x-4 text-sm text-secondary-300">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span>Alto Impacto</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <span>Medio Impacto</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>Bajo Impacto</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-4 bg-secondary-500/10 border border-secondary-500/20 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <svg className="w-5 h-5 text-secondary-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <h3 className="text-secondary-400 font-semibold text-sm mb-1">Cómo usar el Calendario Económico:</h3>
                      <ul className="text-secondary-200 text-xs space-y-1">
                        <li>• <strong>Rojo:</strong> Alto impacto - Puede causar alta volatilidad</li>
                        <li>• <strong>Amarillo:</strong> Medio impacto - Volatilidad moderada esperada</li>
                        <li>• <strong>Verde:</strong> Bajo impacto - Volatilidad mínima</li>
                        <li>• Planifica tus operaciones considerando estos eventos</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Estilos personalizados */}
        <style jsx>{`
          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        `}</style>
      </div>
    </PrivateRoute>
  );
};

export default NoticiasPage;
