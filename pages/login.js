// pages/login.js
import React, { useState, useRef, useEffect } from "react";
import { signIn, signOut, useSession } from "next-auth/react";
import { AiFillTrademarkCircle } from "react-icons/ai";
import Navbar from "../components/navbar";
import Head from "next/head";

const LoginPage = () => {
  const { data: session } = useSession();
  const [profileImage, setProfileImage] = useState(session?.user?.image || null);
  const [uploadedImage, setUploadedImage] = useState(null);
  const [userProgress, setUserProgress] = useState(null);
  const [userMembership, setUserMembership] = useState(null);
  const [isLoadingProgress, setIsLoadingProgress] = useState(true);
  const fileInputRef = useRef(null);

  // Cargar progreso real del usuario
  useEffect(() => {
    const fetchUserProgress = async () => {
      if (session?.user?.email) {
        try {
          setIsLoadingProgress(true);

          // Registrar login
          await fetch('/api/progress', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'record_login',
              data: {}
            })
          });

          // Obtener progreso del usuario
          const response = await fetch(`/api/progress?userEmail=${session.user.email}`);
          const progressData = await response.json();

          // Obtener datos de membresía del usuario
          const membershipResponse = await fetch(`/api/users?userEmail=${session.user.email}`);
          const membershipData = await membershipResponse.json();

          setUserProgress(progressData);
          setUserMembership(membershipData);

          // Actualizar imagen de perfil si existe
          if (progressData.profile?.profileImage) {
            setProfileImage(progressData.profile.profileImage);
          }
        } catch (error) {
          console.error('Error fetching user progress:', error);
          // Fallback a datos por defecto en caso de error
          setUserProgress({
            tiempoEstudio: 0,
            tiempoPagado: 0,
            videosCompletados: 0,
            totalVideos: 0,
            progresoCurso: 0,
            fechaInicio: new Date().toLocaleDateString('es-ES', {
              day: 'numeric',
              month: 'long',
              year: 'numeric'
            }),
            proximoCertificado: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toLocaleDateString('es-ES', {
              day: 'numeric',
              month: 'long',
              year: 'numeric'
            })
          });
        } finally {
          setIsLoadingProgress(false);
        }
      }
    };

    fetchUserProgress();
  }, [session]);

  const handleGoogle = async () => {
    await signIn("google", { callbackUrl: "/membership" });
  };

  const handleSignOutGoogle = async () => {
    try {
      await signOut();
      console.log("User signed out");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const imageData = e.target.result;
        setUploadedImage(imageData);
        setProfileImage(imageData);

        // Guardar imagen en la base de datos
        if (session?.user?.email) {
          try {
            await fetch('/api/progress', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                action: 'update_profile',
                data: {
                  profileImage: imageData
                }
              })
            });
          } catch (error) {
            console.error('Error saving profile image:', error);
          }
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleDownloadCertificate = async () => {
    try {
      if (session?.user?.email) {
        // Primero verificar/generar el certificado
        const response = await fetch(`/api/certificate?userEmail=${session.user.email}`);
        const certificateData = await response.json();

        if (certificateData.success) {
          // Aquí podrías implementar la descarga real del PDF
          // Por ahora, mostraremos una alerta con la información
          alert(`¡Certificado generado exitosamente!

ID del Certificado: ${certificateData.certificate.certificateId}
Fecha de emisión: ${new Date(certificateData.certificate.issuedAt).toLocaleDateString('es-ES')}
Validado por: ${certificateData.certificate.validatedBy}

Tu certificado oficial de RunningPips Academy está listo.`);
        } else {
          alert(`Error: ${certificateData.message || 'No se pudo generar el certificado'}`);
        }
      }
    } catch (error) {
      console.error('Error downloading certificate:', error);
      alert('Error al descargar el certificado. Inténtalo de nuevo.');
    }
  };

  if (!session) {
    return (
      <>
        <Head>
          <title>Iniciar Sesión - RunningPips Academy</title>
        </Head>
        <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
          <Navbar />
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="w-full max-w-md bg-black/40 backdrop-blur-sm border border-secondary-500/30 rounded-xl p-8 shadow-2xl">
              <div className="text-center mb-8">
                <div className="flex items-center justify-center mb-4">
                  <AiFillTrademarkCircle className="text-secondary-500 w-12 h-12 animate-pulse" />
                  <span className="text-3xl font-bold text-secondary-400 ml-2">Bienvenido</span>
                </div>
                <p className="text-secondary-200">
                  Accede a tu cuenta para disfrutar de una experiencia de
                  aprendizaje personalizada y acceder a recursos exclusivos.
                </p>
              </div>
              <button
                onClick={handleGoogle}
                className="w-full bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                Iniciar Sesión / Crear Cuenta
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Mostrar loading mientras se cargan los datos
  if (isLoadingProgress || !userProgress) {
    return (
      <>
        <Head>
          <title>Mi Perfil - RunningPips Academy</title>
        </Head>
        <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
          <Navbar />
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <div className="animate-spin rounded-full h-20 w-20 border-t-2 border-b-2 border-secondary-500 mx-auto mb-4"></div>
              <p className="text-secondary-200 text-lg">Cargando tu progreso...</p>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Mi Perfil - RunningPips Academy</title>
      </Head>
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
        <Navbar />

        {/* Header del Perfil */}
        <div className="bg-black/30 backdrop-blur-sm border-b border-white/10">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-secondary-400">Mi Perfil</h1>
                  <p className="text-secondary-200 text-sm">RunningPips Academy</p>
                </div>
              </div>

              {/* Botones de Navegación */}
              <div className="flex items-center space-x-3">
                <a
                  href="/"
                  className="flex items-center space-x-2 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 shadow-lg"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                  <span>Inicio</span>
                </a>

                <a
                  href="/academy"
                  className="flex items-center space-x-2 bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 shadow-lg"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                  <span>Academia</span>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="max-w-6xl mx-auto px-6 pt-6 pb-2">
          <nav className="flex items-center space-x-2 text-sm">
            <a href="/" className="text-secondary-300 hover:text-secondary-400 transition-colors">
              Inicio
            </a>
            <svg className="w-4 h-4 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <span className="text-secondary-400 font-medium">Mi Perfil</span>
          </nav>
        </div>

        <div className="max-w-6xl mx-auto px-6 py-6">
          {/* Tarjeta Principal del Perfil */}
          <div className="bg-black/40 backdrop-blur-sm border border-secondary-500/30 rounded-xl overflow-hidden mb-8">
            <div className="bg-gradient-to-r from-secondary-500/30 to-secondary-600/30 p-6 border-b border-secondary-500/20">
              <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
                {/* Foto de Perfil */}
                <div className="relative">
                  <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-secondary-500/50 shadow-xl">
                    {profileImage ? (
                      <img
                        src={profileImage}
                        alt="Perfil"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-secondary-400 to-secondary-600 flex items-center justify-center">
                        <svg className="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    )}
                  </div>
                  <button
                    onClick={triggerFileInput}
                    className="absolute -bottom-2 -right-2 w-10 h-10 bg-secondary-500 hover:bg-secondary-600 rounded-full flex items-center justify-center shadow-lg transition-colors"
                  >
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>

                {/* Información del Usuario */}
                <div className="flex-1 text-center md:text-left">
                  <h2 className="text-3xl font-bold text-secondary-400 mb-2">{session.user.name}</h2>
                  <p className="text-secondary-200 mb-4">{session.user.email}</p>
                  <div className="flex flex-wrap justify-center md:justify-start gap-3">
                    {userMembership?.canEnter ? (
                      <span className="px-4 py-2 bg-green-500/20 border border-green-500/30 rounded-full text-green-400 text-sm font-medium">
                        ✅ Miembro Activo
                      </span>
                    ) : (
                      <span className="px-4 py-2 bg-red-500/20 border border-red-500/30 rounded-full text-red-400 text-sm font-medium">
                        ❌ Membresía Inactiva
                      </span>
                    )}

                    {userMembership?.canEnter ? (
                      <span className="px-4 py-2 bg-blue-500/20 border border-blue-500/30 rounded-full text-blue-400 text-sm font-medium">
                        🎓 Estudiante Academia
                      </span>
                    ) : (
                      <span className="px-4 py-2 bg-yellow-500/20 border border-yellow-500/30 rounded-full text-yellow-400 text-sm font-medium">
                        💳 Pago Pendiente
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Alerta de Membresía */}
            {!userMembership?.canEnter && (
              <div className="mx-6 mb-6 bg-gradient-to-r from-red-500/20 to-orange-500/20 border border-red-500/30 rounded-xl p-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-red-400 font-bold text-lg mb-2">Membresía Inactiva</h4>
                    <p className="text-red-200 mb-4">
                      Tu cuenta no tiene acceso activo a la academia. Para acceder a todos los videos, materiales y funcionalidades, necesitas activar tu membresía.
                    </p>
                    <div className="flex flex-wrap gap-3">
                      <a
                        href="/membership"
                        className="bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200"
                      >
                        💳 Activar Membresía
                      </a>
                      <a
                        href="https://wa.me/1234567890"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200"
                      >
                        📱 Contactar Soporte
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Estadísticas del Usuario */}
            <div className="p-6">
              <h3 className="text-xl font-bold text-secondary-400 mb-6 flex items-center space-x-2">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span>Mi Progreso en la Academia</span>
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {/* Tiempo de Estudio */}
                <div className="bg-blue-500/20 border border-blue-500/30 rounded-xl p-6 text-center">
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h4 className="text-blue-400 font-semibold mb-2">Tiempo de Estudio</h4>
                  <p className="text-3xl font-bold text-blue-300 mb-1">{userProgress.tiempoEstudio}</p>
                  <p className="text-blue-200 text-sm">horas completadas</p>
                </div>

                {/* Tiempo Pagado */}
                <div className="bg-green-500/20 border border-green-500/30 rounded-xl p-6 text-center">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h4 className="text-green-400 font-semibold mb-2">Tiempo Pagado</h4>
                  <p className="text-3xl font-bold text-green-300 mb-1">{userProgress.tiempoPagado}</p>
                  <p className="text-green-200 text-sm">días de membresía</p>
                </div>

                {/* Videos Completados */}
                <div className="bg-purple-500/20 border border-purple-500/30 rounded-xl p-6 text-center">
                  <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1" />
                    </svg>
                  </div>
                  <h4 className="text-purple-400 font-semibold mb-2">Videos Completados</h4>
                  <p className="text-3xl font-bold text-purple-300 mb-1">{userProgress.videosCompletados}/{userProgress.totalVideos}</p>
                  <p className="text-purple-200 text-sm">lecciones vistas</p>
                </div>

                {/* Progreso del Curso */}
                <div className="bg-secondary-500/20 border border-secondary-500/30 rounded-xl p-6 text-center">
                  <div className="w-12 h-12 bg-secondary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                  </div>
                  <h4 className="text-secondary-400 font-semibold mb-2">Progreso Total</h4>
                  <p className="text-3xl font-bold text-secondary-300 mb-1">{userProgress.progresoCurso}%</p>
                  <p className="text-secondary-200 text-sm">curso completado</p>
                </div>
              </div>

              {/* Barra de Progreso */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-secondary-400 font-semibold">Progreso del Curso</span>
                  <span className="text-secondary-300">{userProgress.progresoCurso}%</span>
                </div>
                <div className="w-full bg-white/10 rounded-full h-4 overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full transition-all duration-500"
                    style={{ width: `${userProgress.progresoCurso}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {/* Certificado */}
          <div className="bg-black/40 backdrop-blur-sm border border-secondary-500/30 rounded-xl overflow-hidden mb-8">
            <div className="bg-gradient-to-r from-secondary-500/30 to-secondary-600/30 p-6 border-b border-secondary-500/20">
              <h3 className="text-xl font-bold text-secondary-400 flex items-center space-x-2">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                <span>Certificación RunningPips Academy</span>
              </h3>
            </div>

            <div className="p-6">
              <div className="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
                {/* Icono del Certificado */}
                <div className="flex-shrink-0">
                  <div className="w-32 h-32 bg-gradient-to-br from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center shadow-2xl">
                    <svg className="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                  </div>
                </div>

                {/* Información del Certificado */}
                <div className="flex-1 text-center md:text-left">
                  <h4 className="text-2xl font-bold text-secondary-400 mb-3">
                    🏆 Certificado de RunningPips Academy
                  </h4>
                  <p className="text-secondary-200 mb-4 text-lg">
                    <span className="text-secondary-400 font-semibold">Validado por Germayori</span> - La Diosa del Trading
                  </p>
                  <div className="space-y-2 mb-6">
                    <p className="text-secondary-300">
                      <span className="font-semibold">Fecha de inicio:</span> {userProgress.fechaInicio}
                    </p>
                    <p className="text-secondary-300">
                      <span className="font-semibold">Certificación estimada:</span> {userProgress.proximoCertificado}
                    </p>
                  </div>

                  {userProgress.progresoCurso >= 100 ? (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2 text-green-400">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="font-semibold text-lg">¡Felicidades! Curso completado</span>
                      </div>
                      <button
                        onClick={handleDownloadCertificate}
                        className="px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
                      >
                        Descargar Certificado
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2 text-secondary-400">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="font-semibold">En progreso...</span>
                      </div>
                      <p className="text-secondary-300">
                        Completa el {100 - userProgress.progresoCurso}% restante para obtener tu certificado oficial
                        validado por <span className="text-secondary-400 font-semibold">Germayori, la Diosa del Trading</span>.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Información Adicional */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6">
              <h3 className="text-secondary-400 font-bold text-lg mb-4 flex items-center space-x-2">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Información de Membresía</span>
              </h3>
              <ul className="space-y-3 text-secondary-200">
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Acceso completo a la academia</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Señales de trading en vivo</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Soporte de Germayori AI</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Certificación oficial</span>
                </li>
              </ul>
            </div>

            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6">
              <h3 className="text-secondary-400 font-bold text-lg mb-4 flex items-center space-x-2">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Próximos Objetivos</span>
              </h3>
              <ul className="space-y-3 text-secondary-200">
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>
                  <span>Completar {userProgress.totalVideos - userProgress.videosCompletados} videos restantes</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>
                  <span>Alcanzar 100% de progreso</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>
                  <span>Obtener certificación oficial</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>
                  <span>Aplicar estrategias aprendidas</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Botón de Cerrar Sesión */}
          <div className="text-center">
            <button
              onClick={handleSignOutGoogle}
              className="px-8 py-3 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 font-semibold rounded-xl transition-all duration-200"
            >
              Cerrar Sesión
            </button>
          </div>
        </div>

        {/* Botón Flotante de Navegación Rápida */}
        <div className="fixed bottom-6 right-6 flex flex-col space-y-3 z-50">
          <a
            href="/academy"
            className="w-14 h-14 bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 group"
            title="Ir a la Academia"
          >
            <svg className="w-6 h-6 text-white group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </a>

          <a
            href="/"
            className="w-14 h-14 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 group"
            title="Ir al Inicio"
          >
            <svg className="w-6 h-6 text-white group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011 1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
          </a>
        </div>
      </div>
    </>
  );
};

export default LoginPage;
