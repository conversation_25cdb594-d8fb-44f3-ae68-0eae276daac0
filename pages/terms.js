import React from "react";
import Navbar from "../components/navbar";
import Footer from "../components/footer";

function Terms() {
  return (
    <div className="flex flex-col flex-1">
      <Navbar />
        <div className=" min-h-screen">
          <div className="container mx-auto p-5">
            <h1 className="text-3xl font-bold text-center my-5  text-yellow-600">
              Condiciones del Servicio
            </h1>
            <div className=" shadow-md rounded px-8 pt-6 pb-8 mb-4">
              <section className="mb-5">
                <h2 className="text-2xl font-bold text-yellow-600">1. Introducción</h2>
                <p className="mt-2">
                  Bienvenido a Runningpips. Al
                  registrarte, acceder o utilizar nuestros servicios, aceptas
                  estar sujeto a estas Condiciones.
                </p>
              </section>

              <section className="mb-5">
                <h2 className="text-2xl font-bold text-yellow-600">2. <PERSON><PERSON> y Cuenta</h2>
                <p className="mt-2">
                  Para acceder a ciertos servicios, debes registrarte y crear
                  una cuenta en nuestra plataforma. Es tu responsabilidad
                  mantener la confidencialidad de tu contraseña y cuenta.
                </p>
              </section>

              <section className="mb-5">
                <h2 className="text-2xl font-bold text-yellow-600">
                  3. Servicios de Educación Financiera
                </h2>
                <p className="mt-2">
                  Nuestros servicios consisten en la enseñanza de conceptos
                  financieros y de trading con fines educativos. No somos
                  asesores financieros ni ofrecemos asesoramiento personalizado
                  sobre inversiones. La información proporcionada es solo con
                  fines educativos y no garantiza resultados específicos en
                  términos de ganancias o pérdidas en operaciones de trading.
                </p>
              </section>

              <section className="mb-5">
                <h2 className="text-2xl font-bold text-yellow-600">4. Responsabilidad</h2>
                <p className="mt-2">
                  Runningpips y sus afiliados no serán
                  responsables de ninguna pérdida o daño, directo o indirecto,
                  que surja del uso o la confianza en la información
                  proporcionada en nuestros cursos o servicios. Los usuarios
                  asumen la responsabilidad total de sus decisiones de inversión
                  y trading.
                </p>
              </section>

              <section className="mb-5">
                <h2 className="text-2xl font-bold text-yellow-600">
                  5. Modificaciones a las Condiciones
                </h2>
                <p className="mt-2">
                  Nos reservamos el derecho de modificar estas Condiciones en
                  cualquier momento. Las modificaciones entrarán en efecto
                  inmediatamente después de su publicación en nuestra
                  plataforma.
                </p>
              </section>

              <section className="mb-5">
                <h2 className="text-2xl font-bold text-yellow-600">6. Contacto</h2>
                <p className="mt-2">
                  Si tienes preguntas o preocupaciones acerca de estas
                  Condiciones, por favor contacta con nosotros a través de
                  <EMAIL>.
                </p>
              </section>
            </div>
          </div>
        </div>
      <Footer />
    </div>
  );
}

export default Terms;
