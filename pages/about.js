// components/PrivacyPolicy.js
import React from "react";
import Navbar from "../components/navbar";
import Footer from "../components/footer";

const About = () => {
  return (
    <div className="flex flex-col flex-1">
      <Navbar />

      <div className="max-w-4xl mx-auto px-4 py-12">
        <h1 className="text-3xl font-semibold text-secondary-600 mb-4">
          Política de Privacidad
        </h1>
        <p className="mb-2">Fecha de efectividad: 2024-04-01</p>
        <p className="mb-6">
          En Running Pips Academy, respetamos y protegemos la privacidad de
          nuestros usuarios. Esta Política de Privacidad explica cómo
          recopilamos, utilizamos, compartimos y protegemos la información en
          relación con nuestro servicio ubicado en Runningpips.com.
        </p>

        <h2 className="text-2xl font-semibold text-secondary-600 mt-6 mb-3">
          Información que recopilamos
        </h2>
        <ul className="list-disc list-inside mb-6">
          <li>
            <strong>Correo electrónico:</strong> Cuando un usuario se autentica
            mediante Google OAuth en nuestra plataforma, recopilamos el correo
            electrónico asociado a su cuenta de Google. Este correo electrónico
            se utiliza para crear y gestionar su cuenta en nuestra base de datos
            de MongoDB y como identificador único para futuras transacciones en
            nuestra plataforma.
          </li>
        </ul>

        <h2 className="text-2xl font-semibold text-secondary-600 mt-6 mb-3">
          Cómo utilizamos la información
        </h2>
        <ul className="list-disc list-inside mb-6">
          <li>Crear y administrar cuentas de usuario en nuestra plataforma.</li>
          <li>Procesar transacciones y compras de productos.</li>
          <li>
            Mejorar y personalizar la experiencia del usuario en nuestra
            plataforma.
          </li>
          <li>
            Comunicar información importante relativa a la cuenta o a las
            transacciones realizadas.
          </li>
        </ul>

        <h2 className="text-2xl font-semibold text-yellow-600 mt-6 mb-3">
          Compartir y divulgar información
        </h2>
        <p className="mb-6">
          No vendemos, comercializamos ni alquilamos la información personal de
          los usuarios a terceros. Esta información es utilizada exclusivamente
          para los fines descritos en esta política.
        </p>

        <h2 className="text-2xl font-semibold text-yellow-600 mt-6 mb-3">
          Seguridad de la información
        </h2>
        <p className="mb-6">
          Tomamos la seguridad de la información muy en serio y utilizamos
          medidas técnicas y organizativas adecuadas para proteger su
          información personal contra el acceso no autorizado, la alteración, la
          divulgación o la destrucción.
        </p>

        <h2 className="text-2xl font-semibold text-yellow-600 mt-6 mb-3">
          Cambios en esta política de privacidad
        </h2>
        <p className="mb-6">
          Running Pips Academy se reserva el derecho a modificar esta política
          de privacidad en cualquier momento. Cuando hagamos cambios,
          revisaremos la fecha de "última actualización" en la parte superior de
          la política. Animamos a los usuarios a revisar esta política
          periódicamente para estar informados sobre cómo protegemos su
          información personal.
        </p>

        <h2 className="text-2xl font-semibold text-yellow-600 mt-6 mb-3">
          Contacto
        </h2>
        <ul className="list-disc list-inside">
          <li>Email: <EMAIL></li>
        </ul>
      </div>
      <Footer />
    </div>
  );
};

export default About;
