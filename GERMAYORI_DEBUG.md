# 🤖 Germayori AI - Guía de Debugging

## ✅ Cambios Realizados

### 1. **Backend (pages/api/openai.js)**
- ✅ Corregido manejo de contenido de mensajes
- ✅ Mejorado manejo de errores HTTP
- ✅ Agregado logging detallado para debug
- ✅ Validación mejorada de imágenes
- ✅ Soporte para imágenes de alta calidad

### 2. **Frontend (pages/germayori.js)**
- ✅ Mejorado manejo de errores
- ✅ Agregado logging en consola
- ✅ Mejor feedback de errores al usuario

### 3. **Variables de Entorno**
- ✅ Agregada ADMIN_SECRET_KEY faltante
- ✅ Todas las variables configuradas

## 🧪 Cómo Probar

### 1. **Prueba Básica de OpenAI**
```bash
# En terminal
curl -X POST http://localhost:3000/api/test-openai \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. **Verificar Variables de Entorno**
Visita: `http://localhost:3000/api/admin/check-env`

### 3. **Probar Germayori**
1. Ve a: `http://localhost:3000/germayori`
2. Abre la consola del navegador (F12)
3. Envía un mensaje simple: "Hola Germayori"
4. Revisa los logs en consola

## 🔍 Debugging

### **Consola del Servidor (Terminal)**
Busca estos logs:
```
API Key presente: true
API Key formato válido: sk-proj-...
Mensaje: [tu mensaje]
Contenido del mensaje preparado: X elementos
Tipos de contenido: ['text'] o ['text', 'image_url']
```

### **Consola del Navegador (F12)**
Busca estos logs:
```
Respuesta de OpenAI: {success: true, response: "..."}
```

### **Errores Comunes**

#### ❌ Error 401 - API Key Inválida
```
Error 401 con OpenAI: Unauthorized
```
**Solución**: Verifica que tu API key de OpenAI sea válida y tenga créditos

#### ❌ Error 429 - Límite Excedido
```
Error 429 con OpenAI: Too Many Requests
```
**Solución**: Has excedido el límite de uso. Espera o aumenta tu plan

#### ❌ Error de Conexión
```
Error de conexión. Intenta de nuevo. 🌐
```
**Solución**: Verifica tu conexión a internet

## 📊 Monitoreo

### **Uso de OpenAI**
- Revisa tu uso en: https://platform.openai.com/usage
- GPT-4o cuesta ~$0.03 por 1K tokens de entrada
- Cada análisis de imagen puede costar $0.10-0.50

### **Logs Importantes**
1. **API Key válida**: `API Key formato válido: sk-proj-...`
2. **Mensaje procesado**: `Contenido del mensaje preparado: X elementos`
3. **Respuesta exitosa**: `Respuesta de OpenAI: {success: true}`

## 🚀 Próximos Pasos

1. **Ejecuta el proyecto**: `npm run dev`
2. **Prueba la API**: Visita `/api/test-openai`
3. **Verifica variables**: Visita `/api/admin/check-env`
4. **Usa Germayori**: Ve a `/germayori`

## 📞 Soporte

Si sigues teniendo problemas:
1. Revisa los logs del servidor y navegador
2. Verifica tu saldo de OpenAI
3. Confirma que todas las variables estén configuradas
4. Prueba primero con mensajes de texto simple
